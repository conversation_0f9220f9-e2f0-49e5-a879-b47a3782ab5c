/* Admin Login Page Styles */
.admin-login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
  padding: var(--space-4);
}

.login-container {
  width: 100%;
  max-width: 450px;
}

.login-card {
  background: var(--secondary-bg);
  border-radius: var(--radius-2xl);
  padding: var(--space-10);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-xl);
}

/* Login Header */
.login-header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.login-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: var(--accent-gradient);
  border-radius: var(--radius-full);
  margin-bottom: var(--space-4);
  color: var(--text-primary);
}

.login-header h1 {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.login-header p {
  color: var(--text-secondary);
  font-size: var(--text-base);
}

/* Login Form */
.login-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-group label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--text-sm);
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: var(--space-3);
  color: var(--text-muted);
  z-index: 1;
}

.input-wrapper input {
  width: 100%;
  padding: var(--space-3) var(--space-3) var(--space-3) var(--space-10);
  background: var(--primary-bg);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: var(--text-base);
  transition: var(--transition-fast);
}

.input-wrapper input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.input-wrapper input.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input-wrapper input::placeholder {
  color: var(--text-muted);
}

.password-toggle {
  position: absolute;
  right: var(--space-3);
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
}

.password-toggle:hover {
  color: var(--text-primary);
  background: var(--glass-bg);
}

.error-text {
  color: #ef4444;
  font-size: var(--text-sm);
  margin-top: var(--space-1);
}

.auth-error {
  padding: var(--space-3);
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: var(--radius-lg);
  color: #ef4444;
  font-size: var(--text-sm);
  text-align: center;
}

/* Login Button */
.login-btn {
  width: 100%;
  padding: var(--space-4);
  background: var(--accent-gradient);
  border: none;
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: var(--text-lg);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  margin-top: var(--space-4);
}

.login-btn:hover:not(:disabled) {
  background: var(--accent-gradient-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.login-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Login Footer */
.login-footer {
  margin-top: var(--space-8);
  text-align: center;
  padding-top: var(--space-6);
  border-top: 1px solid var(--border-primary);
}

.login-footer p {
  color: var(--text-muted);
  font-size: var(--text-sm);
  line-height: 1.5;
}

.login-footer strong {
  color: var(--accent-primary);
}

/* Responsive Design */
@media (max-width: 480px) {
  .admin-login-page {
    padding: var(--space-2);
  }

  .login-card {
    padding: var(--space-6);
  }

  .login-icon {
    width: 60px;
    height: 60px;
  }

  .login-icon svg {
    width: 32px;
    height: 32px;
  }

  .login-header h1 {
    font-size: var(--text-2xl);
  }

  .input-wrapper input {
    padding: var(--space-3) var(--space-3) var(--space-3) var(--space-8);
  }

  .input-icon {
    left: var(--space-2);
  }

  .password-toggle {
    right: var(--space-2);
  }
}

/* Loading Animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.login-btn:disabled {
  animation: pulse 2s infinite;
}

/* Admin Form Components Styles */

/* Form Field Styles */
.form-field {
  margin-bottom: var(--space-4);
}

.form-field.has-error .form-input {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.form-label .required {
  color: #ef4444;
  margin-left: var(--space-1);
}

.form-input {
  width: 100%;
  padding: var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background: var(--secondary-bg);
  color: var(--text-primary);
  font-size: var(--text-base);
  transition: var(--transition-fast);
}

.form-input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.form-input.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.form-input.error {
  border-color: #ef4444;
}

/* Textarea */
.form-input[type="textarea"] {
  resize: vertical;
  min-height: 100px;
}

/* Select */
.form-input[multiple] {
  min-height: 120px;
}

/* Checkbox */
.checkbox-field {
  margin-bottom: var(--space-4);
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.form-checkbox {
  width: 18px;
  height: 18px;
  accent-color: var(--accent-primary);
}

.checkbox-label {
  font-size: var(--text-base);
  color: var(--text-primary);
  cursor: pointer;
}

/* Password Input */
.password-input-wrapper {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

.password-toggle:hover {
  color: var(--text-primary);
  background: var(--glass-bg);
}

/* File Upload */
.file-upload-area {
  border: 2px dashed var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-8);
  text-align: center;
  cursor: pointer;
  transition: var(--transition-fast);
  background: var(--glass-bg);
}

.file-upload-area:hover,
.file-upload-area.drag-over {
  border-color: var(--accent-primary);
  background: rgba(0, 212, 255, 0.05);
}

.file-input {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.file-upload-content {
  pointer-events: none;
}

.file-upload-content svg {
  color: var(--accent-primary);
  margin-bottom: var(--space-2);
}

.file-upload-content p {
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.file-upload-content small {
  color: var(--text-secondary);
  font-size: var(--text-xs);
}

/* Error and Help Text */
.error-message {
  display: block;
  color: #ef4444;
  font-size: var(--text-xs);
  margin-top: var(--space-1);
}

.help-text {
  display: block;
  color: var(--text-secondary);
  font-size: var(--text-xs);
  margin-top: var(--space-1);
}

/* Modal Styles */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--space-4);
}

.modal-container {
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-small {
  width: 100%;
  max-width: 400px;
}

.modal-medium {
  width: 100%;
  max-width: 600px;
}

.modal-large {
  width: 100%;
  max-width: 900px;
}

.modal-full {
  width: 95vw;
  height: 90vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
}

.modal-title {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.modal-close-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

.modal-close-btn:hover {
  color: var(--text-primary);
  background: var(--glass-bg);
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-6);
}

.modal-form {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-3);
  padding-top: var(--space-6);
  margin-top: auto;
  border-top: 1px solid var(--border-primary);
}

/* Confirm Dialog Styles */
.confirm-backdrop {
  background: rgba(0, 0, 0, 0.8);
}

.confirm-dialog {
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  padding: var(--space-8);
  max-width: 450px;
  width: 100%;
  text-align: center;
}

.confirm-dialog-content {
  margin-bottom: var(--space-6);
}

.confirm-dialog-icon {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-4);
  font-size: var(--text-2xl);
}

.confirm-dialog-warning .confirm-dialog-icon {
  background: rgba(255, 170, 0, 0.2);
  color: #ffaa00;
}

.confirm-dialog-danger .confirm-dialog-icon {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.confirm-dialog-info .confirm-dialog-icon {
  background: rgba(0, 212, 255, 0.2);
  color: var(--accent-primary);
}

.confirm-dialog-success .confirm-dialog-icon {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
}

.confirm-dialog-title {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.confirm-dialog-message {
  color: var(--text-secondary);
  font-size: var(--text-base);
  line-height: 1.5;
}

.confirm-dialog-actions {
  display: flex;
  justify-content: center;
  gap: var(--space-3);
}

/* Button Styles */
.btn {
  padding: var(--space-3) var(--space-6);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-fast);
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--accent-primary);
  color: var(--text-primary);
}

.btn-primary:hover:not(:disabled) {
  background: var(--accent-secondary);
  transform: translateY(-2px);
}

.btn-secondary {
  background: var(--glass-bg);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--border-primary);
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #dc2626;
}

.btn-warning {
  background: #ffaa00;
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background: #f59e0b;
}

.btn-info {
  background: var(--accent-primary);
  color: var(--text-primary);
}

.btn-info:hover:not(:disabled) {
  background: var(--accent-secondary);
}

.btn-success {
  background: #00ff88;
  color: var(--primary-bg);
}

.btn-success:hover:not(:disabled) {
  background: #10b981;
}

/* Data Table Styles */
.data-table {
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  overflow: hidden;
}

.data-table-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.data-table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  background: var(--glass-bg);
}

.data-table-controls {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box svg {
  position: absolute;
  left: var(--space-3);
  color: var(--text-secondary);
  z-index: 1;
}

.search-input {
  padding: var(--space-2) var(--space-3) var(--space-2) var(--space-10);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background: var(--secondary-bg);
  color: var(--text-primary);
  font-size: var(--text-sm);
  width: 250px;
  transition: var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: var(--transition-fast);
}

.filter-btn:hover {
  background: var(--border-primary);
}

.export-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  background: var(--accent-primary);
  color: var(--text-primary);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
}

.export-btn:hover {
  background: var(--accent-secondary);
  transform: translateY(-1px);
}

.bulk-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-4);
  padding: var(--space-3);
  background: rgba(0, 212, 255, 0.1);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(0, 212, 255, 0.2);
}

.bulk-selection-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--text-sm);
}

.selection-count {
  font-weight: 600;
  color: var(--text-primary);
}

.select-all-btn,
.clear-selection-btn {
  background: none;
  border: none;
  color: var(--accent-primary);
  font-size: var(--text-xs);
  cursor: pointer;
  text-decoration: underline;
  padding: var(--space-1);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

.select-all-btn:hover,
.clear-selection-btn:hover {
  background: rgba(0, 212, 255, 0.1);
}

.bulk-action-buttons {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.bulk-action-btn {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-2) var(--space-3);
  background: var(--accent-primary);
  color: var(--text-primary);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  cursor: pointer;
  transition: var(--transition-fast);
}

.bulk-action-btn:hover {
  background: var(--accent-secondary);
}

.bulk-action-btn.danger {
  background: #ef4444;
}

.bulk-action-btn.danger:hover {
  background: #dc2626;
}

.table-container {
  overflow-x: auto;
}

.data-table-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table-table th,
.data-table-table td {
  padding: var(--space-4);
  text-align: left;
  border-bottom: 1px solid var(--border-primary);
}

.data-table-table th {
  background: var(--glass-bg);
  color: var(--text-primary);
  font-weight: 600;
  font-size: var(--text-sm);
  white-space: nowrap;
}

.data-table-table th.sortable {
  cursor: pointer;
  user-select: none;
}

.data-table-table th.sortable:hover {
  background: var(--border-primary);
}

.th-content {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.data-table-table td {
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.data-table-table tr:hover {
  background: var(--glass-bg);
}

.data-table-table tr.clickable {
  cursor: pointer;
}

.data-table-table tr.selected {
  background: rgba(0, 212, 255, 0.1);
}

.select-column {
  width: 40px;
  text-align: center;
}

.select-column input[type="checkbox"] {
  accent-color: var(--accent-primary);
}

.actions-column {
  width: 120px;
  text-align: center;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: var(--space-1);
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
}

.action-btn.view {
  background: rgba(0, 212, 255, 0.2);
  color: var(--accent-primary);
}

.action-btn.edit {
  background: rgba(255, 170, 0, 0.2);
  color: #ffaa00;
}

.action-btn.delete {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.action-btn:hover {
  transform: scale(1.1);
}

.empty-row {
  text-align: center;
  color: var(--text-muted);
  font-style: italic;
  padding: var(--space-8);
}

.filter-row {
  background: var(--glass-bg);
}

.filter-row th {
  padding: var(--space-2);
  border-bottom: 1px solid var(--border-primary);
}

.column-filter-input {
  width: 100%;
  padding: var(--space-2);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background: var(--secondary-bg);
  color: var(--text-primary);
  font-size: var(--text-xs);
  transition: var(--transition-fast);
}

.column-filter-input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.1);
}

.clear-filters-btn {
  padding: var(--space-1) var(--space-2);
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-size: var(--text-xs);
  cursor: pointer;
  transition: var(--transition-fast);
}

.clear-filters-btn:hover {
  background: var(--border-primary);
  color: var(--text-primary);
}

.data-table-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4) var(--space-6);
  border-top: 1px solid var(--border-primary);
  background: var(--glass-bg);
}

.pagination-info {
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.pagination-btn {
  width: 36px;
  height: 36px;
  border: 1px solid var(--border-primary);
  background: var(--secondary-bg);
  color: var(--text-primary);
  border-radius: var(--radius-md);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-sm);
  transition: var(--transition-fast);
}

.pagination-btn:hover:not(:disabled) {
  background: var(--glass-bg);
  border-color: var(--accent-primary);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-btn.active {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
  color: var(--text-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .data-table-header {
    flex-direction: column;
    gap: var(--space-4);
    align-items: stretch;
  }

  .data-table-controls {
    flex-direction: column;
    gap: var(--space-3);
  }

  .search-input {
    width: 100%;
  }

  .bulk-actions {
    flex-direction: column;
    gap: var(--space-3);
    align-items: stretch;
  }

  .bulk-selection-info {
    justify-content: center;
  }

  .bulk-action-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }

  .data-table-pagination {
    flex-direction: column;
    gap: var(--space-4);
    text-align: center;
  }

  .pagination-controls {
    justify-content: center;
  }

  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .data-table-table {
    min-width: 600px;
  }

  .action-buttons {
    flex-direction: column;
    gap: var(--space-1);
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }
}

/* Project Management Styles */
.project-management {
}

.management-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
}

.header-content h2 {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.header-content p {
  color: var(--text-secondary);
  font-size: var(--text-base);
}

.management-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
}

.filter-group {
  display: flex;
  gap: var(--space-3);
}

.filter-select {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background: var(--secondary-bg);
  color: var(--text-primary);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: var(--transition-fast);
}

.filter-select:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.management-content {
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  overflow: hidden;
}

/* Project Form Styles */
.project-form {
  max-height: 70vh;
  overflow-y: auto;
}

.form-grid {
  display: grid;
  gap: var(--space-6);
}

.form-section {
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
}

.form-section h3 {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-2);
  border-bottom: 1px solid var(--border-primary);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-3);
  padding-top: var(--space-6);
  margin-top: var(--space-6);
  border-top: 1px solid var(--border-primary);
}

/* Array Input Styles */
.array-input {
  margin-bottom: var(--space-4);
}

.array-input label {
  display: block;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.array-input-row {
  display: flex;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
}

.array-input-row input {
  flex: 1;
}

.array-items {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.array-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--accent-primary);
  color: var(--text-primary);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
}

.remove-btn {
  background: none;
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  font-size: var(--text-lg);
  line-height: 1;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  transition: var(--transition-fast);
}

.remove-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Hero Image Preview */
.uploaded-image-preview {
  margin-top: var(--space-3);
}

.image-preview-container {
  position: relative;
  display: inline-block;
  margin-bottom: var(--space-2);
}

.image-preview {
  max-width: 100%;
  max-height: 200px;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
  display: block;
}

.remove-hero-image-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 28px;
  height: 28px;
  background: rgba(239, 68, 68, 0.9);
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
  line-height: 1;
}

.remove-hero-image-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
}

.image-url {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  word-break: break-all;
  margin-top: var(--space-1);
}

/* Amenity Items */
.amenity-input {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.amenity-items {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  margin-top: var(--space-4);
}

.amenity-item {
  display: flex;
  align-items: flex-start;
  
  padding: var(--space-3);
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

.amenity-item:hover {
  background: var(--glass-bg-hover);
  border-color: var(--border-secondary);
}

.amenity-content {
  flex: 1;
}

.amenity-header {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-1);
}

.amenity-icon {
  font-size: 1.2em;
  min-width: 24px;
}

.amenity-name {
  font-weight: 600;
  color: var(--text-primary);
}

.amenity-description {
  color: var(--text-secondary);
  font-size: 0.9em;
  margin: 0;
  line-height: 1.4;
}

/* Unit Type Styles */
.unit-type-input {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.unit-type-items {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3);
  margin-top: var(--space-4);
}

.unit-type-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-full);
  transition: var(--transition-fast);
}

.unit-type-item:hover {
  background: var(--glass-bg-hover);
  border-color: var(--border-secondary);
}

.unit-type-name {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9em;
}

/* Image Upload Section */
.image-upload-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.uploaded-image-preview {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  padding: var(--space-3);
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
}

.image-preview {
  max-width: 200px;
  max-height: 150px;
  object-fit: cover;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
}

.image-url {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0;
  word-break: break-all;
}

/* File Upload Area Improvements */
.file-upload-area {
  cursor: pointer;
  transition: all var(--transition-fast);
}

.file-upload-area:hover {
  border-color: var(--accent-primary);
  background: var(--glass-bg-hover);
}

.file-upload-area.drag-over {
  border-color: var(--accent-primary);
  background: var(--accent-primary-light);
  transform: scale(1.02);
}

.file-upload-content {
  pointer-events: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  color: var(--text-secondary);
}

.file-upload-content p {
  margin: 0;
  font-weight: 500;
}

.file-upload-content small {
  color: var(--text-tertiary);
}

/* Multiple Images Preview */
.multiple-images-preview {
  margin-top: var(--space-4);
}

.multiple-images-preview h4 {
  margin: 0 0 var(--space-3) 0;
  color: var(--text-primary);
  font-size: var(--text-lg);
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: var(--space-3);
}

.image-item {
  position: relative;
  border-radius: var(--radius-md);
  overflow: hidden;
  border: 1px solid var(--border-primary);
  background: var(--glass-bg);
}

.image-item .image-preview {
  width: 100%;
  height: 120px;
  object-fit: cover;
  display: block;
}

.remove-image-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background: rgba(255, 255, 255, 0.9);
  color: var(--danger);
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: var(--transition-fast);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.remove-image-btn:hover {
  background: var(--danger);
  color: white;
  transform: scale(1.1);
}

/* File Preview (for brochures) */
.uploaded-file-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-3);
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  margin-top: var(--space-3);
}

.file-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex: 1;
}

.file-icon {
  font-size: 2em;
  min-width: 40px;
}

.file-details {
  flex: 1;
}

.file-name {
  margin: 0 0 var(--space-1) 0;
  font-weight: 600;
  color: var(--text-primary);
}

.file-url {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  word-break: break-all;
}

.remove-file-btn {
  background: var(--danger);
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  transition: var(--transition-fast);
  flex-shrink: 0;
}

.remove-file-btn:hover {
  background: var(--danger-dark);
  transform: scale(1.1);
}

/* Table Cell Styles */
.project-info strong {
  display: block;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.project-info small {
  color: var(--text-secondary);
  font-size: var(--text-xs);
}

.category-badge,
.status-badge,
.featured-badge {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.category-badge.residential {
  background: rgba(0, 212, 255, 0.2);
  color: var(--accent-primary);
}

.category-badge.commercial {
  background: rgba(255, 170, 0, 0.2);
  color: #ffaa00;
}

.category-badge.mixed {
  background: rgba(147, 51, 234, 0.2);
  color: #9333ea;
}

.status-badge.upcoming {
  background: rgba(255, 170, 0, 0.2);
  color: #ffaa00;
}

.status-badge.ongoing {
  background: rgba(0, 212, 255, 0.2);
  color: var(--accent-primary);
}

.status-badge.completed {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
}

.featured-badge.yes {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
}

.featured-badge.no {
  background: rgba(107, 114, 128, 0.2);
  color: #6b7280;
}

.progress-bar {
  position: relative;
  width: 80px;
  height: 20px;
  background: var(--glass-bg);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--accent-gradient);
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
}

.progress-bar span {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: var(--text-xs);
  font-weight: 600;
  color: var(--text-primary);
  z-index: 1;
}

/* Apartment Management Styles */
.apartment-management {
  max-width: 1400px;
}

.apartment-info strong {
  display: block;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.apartment-info small {
  color: var(--text-secondary);
  font-size: var(--text-xs);
}

.price-info strong {
  display: block;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.price-info small {
  color: var(--text-secondary);
  font-size: var(--text-xs);
}

.availability-info {
  text-align: center;
}

.availability-badge {
  display: block;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: 600;
  margin-bottom: var(--space-1);
}

.availability-badge.available {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
}

.availability-badge.sold-out {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.availability-info small {
  color: var(--text-secondary);
  font-size: var(--text-xs);
}

.action-btn.book {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
}

.action-btn.release {
  background: rgba(255, 170, 0, 0.2);
  color: #ffaa00;
}

.booking-form {
  padding: var(--space-4) 0;
}

.booking-form p {
  margin-bottom: var(--space-3);
  color: var(--text-secondary);
}

.booking-form p strong {
  color: var(--text-primary);
}

/* Apartment Form Styles */
.apartment-form {
  max-height: 70vh;
  overflow-y: auto;
}

/* Lead Management Styles */
.lead-management {
  max-width: 1600px;
}

.lead-info strong {
  display: block;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.lead-info small {
  display: block;
  color: var(--text-secondary);
  font-size: var(--text-xs);
  margin-bottom: var(--space-1);
}

.lead-type-badge {
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.lead-type-badge.inquiry {
  background: rgba(0, 212, 255, 0.2);
  color: var(--accent-primary);
}

.lead-type-badge.site_visit {
  background: rgba(255, 170, 0, 0.2);
  color: #ffaa00;
}

.lead-type-badge.brochure_download {
  background: rgba(147, 51, 234, 0.2);
  color: #9333ea;
}

.lead-type-badge.callback_request {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.lead-type-badge.booking_interest {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
}

.priority-badge {
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.priority-badge.low {
  background: rgba(107, 114, 128, 0.2);
  color: #6b7280;
}

.priority-badge.medium {
  background: rgba(0, 212, 255, 0.2);
  color: var(--accent-primary);
}

.priority-badge.high {
  background: rgba(255, 170, 0, 0.2);
  color: #ffaa00;
}

.priority-badge.urgent {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.action-btn.call {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
}

.action-btn.email {
  background: rgba(147, 51, 234, 0.2);
  color: #9333ea;
}

.action-btn.status {
  background: rgba(255, 170, 0, 0.2);
  color: #ffaa00;
}

.status-form {
  padding: var(--space-4) 0;
}

.status-form p {
  margin-bottom: var(--space-3);
  color: var(--text-secondary);
}

.status-form p strong {
  color: var(--text-primary);
}

.status-form .status-badge {
  margin-left: var(--space-2);
}

/* Lead Form Styles */
.lead-form {
  max-height: 70vh;
  overflow-y: auto;
}

/* User Management Styles */
.user-management {
  max-width: 1400px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  overflow: hidden;
  flex-shrink: 0;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: var(--accent-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  font-weight: 600;
  font-size: var(--text-lg);
}

.user-details strong {
  display: block;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.user-details small {
  color: var(--text-secondary);
  font-size: var(--text-xs);
}

.role-badge {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.role-badge.admin {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.role-badge.user {
  background: rgba(0, 212, 255, 0.2);
  color: var(--accent-primary);
}

.status-badge.active {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
}

.status-badge.inactive {
  background: rgba(107, 114, 128, 0.2);
  color: #6b7280;
}

.action-btn.activate {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
}

.action-btn.deactivate {
  background: rgba(255, 170, 0, 0.2);
  color: #ffaa00;
}

/* User Form Styles */
.user-form {
  max-height: 70vh;
  overflow-y: auto;
}

/* Settings Management Styles */
.settings-management {
  max-width: 1400px;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-6);
}

.settings-card {
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  transition: var(--transition-fast);
}

.settings-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.settings-card-header {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  background: var(--glass-bg);
}

.settings-icon {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-lg);
  background: var(--accent-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  flex-shrink: 0;
}

.settings-info {
  flex: 1;
}

.settings-info h3 {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.settings-info p {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  line-height: 1.4;
}

.settings-card-body {
  padding: var(--space-6);
}

.settings-field {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3) 0;
  border-bottom: 1px solid var(--border-primary);
}

.settings-field:last-child {
  border-bottom: none;
}

.field-label {
  font-weight: 500;
  color: var(--text-primary);
  font-size: var(--text-sm);
}

.field-value {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  text-align: right;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Settings Form Styles */
.settings-form {
  max-height: 70vh;
  overflow-y: auto;
}

.settings-form h4 {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-3);
  padding-bottom: var(--space-2);
  border-bottom: 1px solid var(--border-primary);
}

.business-hours-day {
  margin-bottom: var(--space-4);
  padding: var(--space-4);
  background: var(--glass-bg);
  border-radius: var(--radius-lg);
}

.business-hours-day h4 {
  margin-bottom: var(--space-3);
  border-bottom: none;
  padding-bottom: 0;
}

.integration-section {
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background: var(--glass-bg);
  border-radius: var(--radius-lg);
}

.integration-section h4 {
  margin-bottom: var(--space-3);
  border-bottom: none;
  padding-bottom: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .form-input {
    padding: var(--space-2);
    font-size: var(--text-sm);
  }

  .modal-body {
    padding: var(--space-4);
  }

  .data-table-table th,
  .data-table-table td {
    padding: var(--space-2);
    font-size: var(--text-xs);
  }

  .management-header {
    flex-direction: column;
    gap: var(--space-4);
    align-items: stretch;
  }

  .management-filters {
    flex-direction: column;
    gap: var(--space-4);
  }

  .filter-group {
    flex-direction: column;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .array-input-row {
    flex-direction: column;
  }

  .management-header {
    flex-direction: column;
    gap: var(--space-4);
    align-items: stretch;
    text-align: center;
  }

  .management-filters {
    flex-direction: column;
    gap: var(--space-4);
  }

  .filter-group {
    flex-direction: column;
    gap: var(--space-3);
  }

  .export-btn {
    width: 100%;
    justify-content: center;
  }

  .settings-grid {
    grid-template-columns: 1fr;
  }

  .settings-card-header {
    flex-direction: column;
    text-align: center;
    gap: var(--space-3);
  }

  .settings-field {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }

  .field-value {
    text-align: left;
    max-width: none;
  }
}

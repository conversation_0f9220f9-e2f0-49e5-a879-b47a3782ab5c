/* Scroll to <PERSON> Button */
.scroll-to-top {
  position: fixed;
  bottom: var(--space-8);
  right: var(--space-8);
  width: 50px;
  height: 50px;
  background: var(--accent-gradient);
  border: none;
  border-radius: 50%;
  color: var(--text-primary);
  font-size: var(--text-xl);
  cursor: pointer;
  z-index: var(--z-fixed);
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-normal);
}

.scroll-to-top:hover {
  box-shadow: var(--shadow-glow);
  background: var(--accent-gradient-hover);
}

.scroll-arrow {
  font-weight: bold;
  font-size: var(--text-2xl);
}

@media (max-width: 768px) {
  .scroll-to-top {
    bottom: var(--space-6);
    right: var(--space-6);
    width: 45px;
    height: 45px;
    font-size: var(--text-lg);
  }
}

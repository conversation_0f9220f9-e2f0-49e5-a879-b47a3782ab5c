/* Admin Dashboard Styles */
.admin-dashboard {

  min-height: 100vh;
  background: var(--primary-bg);
}

/* Sidebar */
.admin-sidebar {
  width: 280px;
  background: var(--secondary-bg);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  overflow-y: auto;
}

.sidebar-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
}

.sidebar-header h2 {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.sidebar-header p {
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.sidebar-nav {
  flex: 1;
  padding: var(--space-4) 0;
}

.nav-item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-6);
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: var(--text-base);
  cursor: pointer;
  transition: var(--transition-fast);
  text-align: left;
}

.nav-item:hover {
  background: var(--glass-bg);
  color: var(--text-primary);
}

.nav-item.active {
  background: var(--accent-primary);
  color: var(--text-primary);
}

.sidebar-footer {
  padding: var(--space-4) var(--space-6);
  border-top: 1px solid var(--border-primary);
}

.logout-btn {
  width: 100%;
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: none;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  font-size: var(--text-base);
  cursor: pointer;
  transition: var(--transition-fast);
}

.logout-btn:hover {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
}

/* Main Content */
.admin-main {
  flex: 1;
  margin-left: 280px;
  display: flex;
  flex-direction: column;
}

.admin-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  background: var(--secondary-bg);
}

.admin-header h1 {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--text-primary);
}

.admin-content {
  flex: 1;
  padding: var(--space-6);
  overflow-y: auto;
}

/* Overview Section */
.overview-section {
  max-width: 1200px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.stat-card {
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  transition: var(--transition-fast);
}

.stat-card:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-glow);
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: var(--accent-gradient);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  font-size: var(--text-2xl);
}

.stat-info h3 {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.stat-info p {
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

/* Projects Section */
.projects-section {
  max-width: 1400px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
}

.section-header h2 {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
}

.add-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  background: var(--accent-primary);
  color: var(--text-primary);
  border: none;
  border-radius: var(--radius-lg);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-fast);
}

.add-btn:hover {
  background: var(--accent-secondary);
  transform: translateY(-2px);
}

/* Projects Table */
.projects-table {
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  overflow: hidden;
}

.projects-table table {
  width: 100%;
  border-collapse: collapse;
}

.projects-table th,
.projects-table td {
  padding: var(--space-4);
  text-align: left;
  border-bottom: 1px solid var(--border-primary);
}

.projects-table th {
  background: var(--glass-bg);
  color: var(--text-primary);
  font-weight: 600;
  font-size: var(--text-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.projects-table td {
  color: var(--text-secondary);
}

.projects-table tr:hover {
  background: var(--glass-bg);
}

.project-info strong {
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--space-1);
}

.project-info small {
  color: var(--text-muted);
  font-size: var(--text-xs);
}

.status-badge {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.upcoming {
  background: rgba(255, 170, 0, 0.2);
  color: #ffaa00;
}

.status-badge.ongoing {
  background: rgba(0, 212, 255, 0.2);
  color: var(--accent-primary);
}

.status-badge.completed {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
}

.action-buttons {
  display: flex;
  gap: var(--space-2);
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
}

.action-btn.view {
  background: rgba(0, 212, 255, 0.2);
  color: var(--accent-primary);
}

.action-btn.edit {
  background: rgba(255, 170, 0, 0.2);
  color: #ffaa00;
}

.action-btn.delete {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.action-btn:hover {
  transform: scale(1.1);
}

/* Responsive Design */
.mobile-nav-toggle {
  display: none;
  background: none;
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  padding: var(--space-2);
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  z-index: 1001;
}

@media (max-width: 768px) {
  .admin-sidebar {
    position: fixed;
    left: -280px;
    top: 0;
    height: 100%;
    z-index: 1000;
    transition: left 0.3s ease-in-out;
  }

  .admin-dashboard.sidebar-open .admin-sidebar {
    left: 0;
  }

  .admin-main {
    margin-left: 0;
  }

  .mobile-nav-toggle {
    display: block;
  }

  .admin-header {
    position: relative;
  }
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

@media (min-width: 769px) {
  .overlay {
    display: none;
  }
}
.file-field-container {
  width: 100%;
}

.file-upload-area {
  border: 2px dashed var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-8);
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--glass-bg);
  margin-bottom: var(--space-4);
}

.file-upload-area:hover,
.file-upload-area.drag-over {
  border-color: var(--primary);
  background: var(--primary-light);
}

.file-input {
  display: none;
}

.file-upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-3);
  color: var(--text-secondary);
}

.file-upload-content svg {
  color: var(--primary);
}

.file-upload-content p {
  margin: 0;
  font-weight: 500;
}

.file-upload-content small {
  color: var(--text-muted);
  display: block;
  margin-top: var(--space-1);
}

/* Selected Files Display */
.selected-files {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: var(--space-4);
}

.selected-files h4 {
  margin: 0 0 var(--space-3) 0;
  color: var(--text-primary);
  font-size: var(--text-sm);
  font-weight: 600;
}

.file-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.selected-file {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-2) var(--space-3);
  background: var(--bg-primary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  font-size: var(--text-sm);
}

.file-name {
  flex: 1;
  color: var(--text-primary);
  font-weight: 500;
  margin-right: var(--space-2);
  word-break: break-word;
}

.file-size {
  color: var(--text-muted);
  font-size: var(--text-xs);
  margin-right: var(--space-2);
}

.remove-file-btn {
  background: var(--danger);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  padding: var(--space-1);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.remove-file-btn:hover {
  background: var(--danger-dark);
}

.file-actions {
  margin-top: var(--space-3);
  padding-top: var(--space-3);
  border-top: 1px solid var(--border-secondary);
}

.clear-all-btn {
  background: var(--warning);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-xs);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.clear-all-btn:hover {
  background: var(--warning-dark);
}

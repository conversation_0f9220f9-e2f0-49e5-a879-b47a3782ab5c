/* Project Detail Page Styles */
.project-detail {
  min-height: 100vh;
}



/* Loading and Error States */
.project-detail-loading {
  min-height: 100vh;
  padding: var(--space-8) 0;
  background: var(--bg-primary);
}

.project-detail-error {
  min-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-placeholder {
  max-width: 1200px;
  margin: 0 auto;
}

.skeleton-hero {
  width: 100%;
  height: 400px;
  background: var(--glass-bg);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-8);
  position: relative;
  overflow: hidden;
}

.skeleton-hero::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: skeleton-shimmer 1.5s infinite;
}

.skeleton-content {
  padding: 0 var(--space-4);
}

.skeleton-line {
  height: 24px;
  background: var(--glass-bg);
  border-radius: var(--radius-sm);
  margin-bottom: var(--space-3);
  position: relative;
  overflow: hidden;
}

.skeleton-line.short {
  width: 60%;
}

.skeleton-line::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: skeleton-shimmer 1.5s infinite;
}

.skeleton-boxes {
  display: flex;
  gap: var(--space-4);
  margin-top: var(--space-6);
}

.skeleton-box {
  flex: 1;
  height: 80px;
  background: var(--glass-bg);
  border-radius: var(--radius-md);
  position: relative;
  overflow: hidden;
}

.skeleton-box::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: skeleton-shimmer 1.5s infinite;
}

@keyframes skeleton-shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.error-content {
  text-align: center;
  max-width: 400px;
}

.error-content h2 {
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.error-content p {
  color: var(--text-secondary);
  margin-bottom: var(--space-6);
}

.back-to-projects {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  background: var(--accent-gradient);
  color: white;
  text-decoration: none;
  border-radius: var(--radius-lg);
  font-weight: 600;
  transition: var(--transition-normal);
}

.back-to-projects:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Hero Section */
.project-hero {
  position: relative;
  height: 100vh;
  min-height: 600px;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0.6) 100%
  );
}

.hero-content {
  position: relative;
  z-index: 2;
  width: 100%;
}

.hero-info {
  max-width: 600px;
  color: white;
}

.back-link {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-weight: 500;
  margin-bottom: var(--space-6);
  transition: var(--transition-normal);
}

.back-link:hover {
  color: white;
  transform: translateX(-4px);
}

.project-badges {
  display: flex;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.status-badge {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.featured-badge {
  background: var(--accent-gradient);
  color: white;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.project-title {
  font-size: clamp(1.5rem, 5vw, 1.5rem);
  font-weight: 800;
  margin-bottom: var(--space-4);
  line-height: 1.1;
  color: var(--accent-primary);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.project-location {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-lg);
  margin-bottom: var(--space-6);
  color: rgba(255, 255, 255, 0.9);
}

.project-description {
  font-size: var(--text-lg);
  line-height: 1.6;
  margin-bottom: var(--space-8);
  color: rgba(255, 255, 255, 0.9);
}

.hero-actions {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.contact-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: 600;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: var(--transition-normal);
  font-size: var(--text-base);
}

.contact-btn.primary {
  background: var(--accent-gradient);
  color: white;
}

.contact-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.contact-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.contact-btn.primary:hover {
  background: var(--accent-gradient-hover);
}

.contact-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Project Details Section */
.project-details-section {
  padding: var(--space-12) 0;
  background: var(--bg-primary);
}

.details-grid {
  max-width: 1200px;
  margin: 0 auto;
}

/* Key Information Card */
.key-information-card {
 
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  backdrop-filter: blur(10px);
  margin-bottom: var(--space-8);
}

.key-information-card h3 {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
  text-align: center;
}

.key-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-3);
}

.key-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
  transition: var(--transition-fast);
}

.key-info-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--accent-primary);
}

.info-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.info-value {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  text-align: right;
}

/* Project Specs Section */
.project-specs-section {
  padding: var(--space-12) 0;
  background: var(--bg-secondary);
}

.specs-content {
  max-width: 1200px;
  margin: 0 auto;
}

.unit-types-section,
.specifications-section {
  margin-bottom: var(--space-12);
}

.unit-types-section:last-child,
.specifications-section:last-child {
  margin-bottom: 0;
}

.unit-types-section h3,
.specifications-section h3 {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-6);
  text-align: center;
}

.unit-types-grid {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3);
  justify-content: center;
  max-width: 800px;
  margin: 0 auto;
}

.unit-type-card {
  background: var(--glass-bg);
  border: 2px solid var(--border-primary);
      border-radius: var(--text-base);
    padding: var(--space-2) var(--space-6);
  text-align: center;
  transition: var(--transition-normal);
  display: inline-flex;
  align-items: center;
  min-width: fit-content;
}

.unit-type-card:hover {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.unit-type-card:hover .unit-type-name {
  color: white;
}

.unit-type-name {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  transition: var(--transition-fast);
  white-space: nowrap;
}

.specs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-6);
}

.spec-card {
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  text-align: center;
  transition: var(--transition-normal);
}

.spec-card:hover {
  background: var(--glass-bg-hover);
  border-color: var(--accent-primary);
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.spec-card .spec-value {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--accent-primary);
  margin-bottom: var(--space-2);
}

.spec-card .spec-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Amenities & Features Section */
.amenities-features-section {
  padding: var(--space-12) 0;
  background: var(--bg-primary);
}

.amenities-features-content {
  max-width: 1200px;
  margin: 0 auto;
}

.amenities-section,
.features-section,
.approvals-section {
  margin-bottom: var(--space-12);
}

.amenities-section:last-child,
.features-section:last-child,
.approvals-section:last-child {
  margin-bottom: 0;
}

.amenities-section h3,
.features-section h3,
.approvals-section h3 {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-8);
  text-align: center;
}

.amenities-grid,
.features-grid,
.approvals-grid {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3);
  justify-content: center;
}

.amenity-card,
.feature-card,
.approval-card {
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
      border-radius: var(--text-base);
    padding: var(--space-3) var(--space-6);
  text-align: center;
  transition: var(--transition-normal);
  display: inline-flex;
  align-items: center;
  min-width: fit-content;
}

.amenity-card:hover,
.feature-card:hover,
.approval-card:hover {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.amenity-card:hover .amenity-name,
.feature-card:hover .feature-name,
.approval-card:hover .approval-name {
  color: white;
}

.amenity-name,
.feature-name,
.approval-name {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
}

/* Project Gallery Section */
.project-gallery-section {
  padding: var(--space-12) 0;
  background: var(--bg-secondary);
}

.gallery-content {
  max-width: 1200px;
  margin: 0 auto;
}

.gallery-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-8);
}

.gallery-header h3 {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
}

.view-all-btn {
  background: var(--accent-primary);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-3) var(--space-6);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
}

.view-all-btn:hover {
  background: var(--accent-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.gallery-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-4);
  height: 400px;
}

.main-image {
  position: relative;
  border-radius: var(--radius-lg);
  overflow: hidden;
  cursor: pointer;
  transition: var(--transition-normal);
}

.main-image:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-xl);
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-2);
}

.thumbnail-item {
  position: relative;
  border-radius: var(--radius-md);
  overflow: hidden;
  cursor: pointer;
  transition: var(--transition-normal);
}

.thumbnail-item:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay,
.more-images-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  opacity: 0;
  transition: var(--transition-fast);
}

.main-image:hover .image-overlay,
.thumbnail-item:hover .more-images-overlay {
  opacity: 1;
}

.image-overlay {
  flex-direction: column;
  gap: var(--space-2);
}

.more-images-count {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: 700;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  z-index: 2;
  min-width: 40px;
  text-align: center;
}

/* Image Gallery Modal */
.image-gallery-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(10px);
}

.image-gallery-modal .gallery-content {
  position: relative;
  width: 90%;
  max-width: 1200px;
  height: 80%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-gallery {
  position: absolute;
  top: -50px;
  right: 0;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: var(--transition-fast);
  z-index: 10001;
}

.close-gallery:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.gallery-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: var(--transition-fast);
  z-index: 10001;
}

.gallery-nav:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-50%) scale(1.1);
}

.gallery-nav.prev {
  left: 20px;
}

.gallery-nav.next {
  right: 20px;
}

.gallery-image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gallery-image-container .gallery-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: var(--radius-lg);
}

.gallery-thumbnails {
  position: absolute;
  bottom: -80px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: var(--space-2);
  max-width: 100%;
  overflow-x: auto;
  padding: var(--space-2);
}

.gallery-thumbnail {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: var(--radius-sm);
  cursor: pointer;
  opacity: 0.6;
  transition: var(--transition-fast);
  border: 2px solid transparent;
}

.gallery-thumbnail:hover {
  opacity: 0.8;
}

.gallery-thumbnail.active {
  opacity: 1;
  border-color: var(--accent-primary);
}

/* Form Modals */
.brochure-form-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(10px);
}

.brochure-form-content {
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  width: 90%;
  max-width: 500px;
  position: relative;
  backdrop-filter: blur(20px);
}

.form-header {
  text-align: center;
  margin-bottom: var(--space-6);
  position: relative;
}

.form-header h3 {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.form-header p {
  color: var(--text-secondary);
  font-size: var(--text-base);
}

.close-form {
  position: absolute;
  top: -10px;
  right: -10px;
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition-fast);
}

.close-form:hover {
  background: var(--glass-bg-hover);
  color: var(--text-primary);
  transform: scale(1.1);
}

.form-group {
  margin-bottom: var(--space-4);
}

.form-group label {
  display: block;
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.form-group input {
  width: 100%;
  padding: var(--space-3);
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: var(--text-base);
  transition: var(--transition-fast);
}

.form-group input:focus {
  outline: none;
  border-color: var(--accent-primary);
  background: var(--glass-bg-hover);
}

.form-group input::placeholder {
  color: var(--text-muted);
}

.form-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
  margin-top: var(--space-6);
}

.btn-cancel {
  padding: var(--space-3) var(--space-6);
  background: transparent;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-size: var(--text-base);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
}

.btn-cancel:hover {
  background: var(--glass-bg);
  color: var(--text-primary);
}

.btn-download {
  padding: var(--space-3) var(--space-6);
  background: var(--accent-primary);
  border: none;
  border-radius: var(--radius-md);
  color: white;
  font-size: var(--text-base);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  text-decoration: none;
}

.btn-download:hover {
  background: var(--accent-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}



/* Responsive Design */
@media (max-width: 768px) {
  .project-details-section,
  .project-specs-section,
  .amenities-features-section,
  .project-gallery-section {
    padding: var(--space-8) 0;
  }

  .key-information-card {
    padding: var(--space-4);
    margin-bottom: var(--space-6);
  }

  .key-information-card h3 {
    font-size: var(--text-lg);
    margin-bottom: var(--space-3);
  }

  .key-info-grid {
    grid-template-columns: 1fr 1fr;
    gap: var(--space-2);
  }

  .key-info-item {
    padding: var(--space-2) var(--space-3);
  }

  .info-label {
    font-size: var(--text-xs);
  }

  .info-value {
    font-size: var(--text-sm);
  }

  .unit-types-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--space-3);
  }

  .specs-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-4);
  }

  .amenities-grid,
  .features-grid,
  .approvals-grid {
    gap: var(--space-2);
  }

  .gallery-header {
    flex-direction: column;
    gap: var(--space-4);
    text-align: center;
  }

  .gallery-grid {
    grid-template-columns: 1fr;
    height: auto;
    gap: var(--space-4);
  }

  .main-image {
    height: 250px;
  }

  .thumbnail-grid {
    grid-template-columns: repeat(3, 1fr);
    height: 120px;
  }

  .container {
    padding: 0 var(--space-4);
  }
}

@media (max-width: 480px) {
  .project-details-section {
    padding: var(--space-6) 0;
  }

  .key-information-card {
    padding: var(--space-3);
    margin-bottom: var(--space-4);
  }

  .key-information-card h3 {
    font-size: var(--text-base);
    margin-bottom: var(--space-2);
  }

  .key-info-item {
    padding: var(--space-2);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-1);
  }

  .info-label {
    font-size: var(--text-xs);
    color: var(--text-muted);
  }

  .info-value {
    font-size: var(--text-sm);
    font-weight: 700;
    text-align: left;
  }

  .unit-types-grid {
    gap: var(--space-2);
  }

 

  .unit-type-name {
    font-size: var(--text-sm);
  }

  .specs-grid {
    grid-template-columns: 1fr;
  }

  .amenities-grid,
  .features-grid,
  .approvals-grid {
    gap: var(--space-2);
  }


  .amenity-name,
  .feature-name,
  .approval-name {
    font-size: var(--text-xs);
  }

  .thumbnail-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .container {
    padding: 0 var(--space-3);
  }

  /* Modal responsive styles */
  .brochure-form-content {
    width: 95%;
    padding: var(--space-6);
    margin: var(--space-4);
  }

  .form-header h3 {
    font-size: var(--text-xl);
  }

  .form-actions {
    flex-direction: column;
  }

  .btn-download,
  .btn-cancel {
    width: 100%;
    justify-content: center;
  }

  /* Gallery modal responsive */
  .image-gallery-modal .gallery-content {
    width: 95%;
    height: 70%;
  }

  .close-gallery {
    top: -40px;
    right: 10px;
  }

  .gallery-nav {
    width: 40px;
    height: 40px;
  }

  .gallery-nav.prev {
    left: 10px;
  }

  .gallery-nav.next {
    right: 10px;
  }

  .gallery-thumbnails {
    bottom: -60px;
    gap: var(--space-1);
  }

  .gallery-thumbnail {
    width: 50px;
    height: 50px;
  }

  .more-images-count {
    padding: var(--space-1) var(--space-2);
    font-size: var(--text-xs);
    min-width: 30px;
  }
}

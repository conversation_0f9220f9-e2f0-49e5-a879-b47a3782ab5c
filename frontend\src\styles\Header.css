/* Header Styles */
.header {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
  background: rgba(10, 10, 10, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-primary);
  transition: var(--transition-normal);
}

.header--scrolled {
  background: rgba(10, 10, 10, 0.95);
  box-shadow: var(--shadow-lg);
}

.header__content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) 0;
  min-height: 80px;
}

/* Logo */
.header__logo {
  z-index: var(--z-fixed);
}

.header__logo a {
  display: flex;
  align-items: center;
  font-size: var(--text-2xl);
  font-weight: 700;
  text-decoration: none;
}

.logo__text {
  color: var(--accent-primary);
  margin-right: var(--space-1);
}

.logo__accent {
  color: var(--accent-primary);
  position: relative;
}

.logo__accent::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--accent-gradient);
  border-radius: var(--radius-full);
}

/* Desktop Navigation */
.header__nav {
  display: none;
}

.nav__list {
  display: flex;
  list-style: none;
  gap: var(--space-8);
}

.nav__item {
  position: relative;
}

.nav__link {
  color: var(--text-secondary);
  font-weight: 500;
  padding: var(--space-2) 0;
  position: relative;
  transition: var(--transition-fast);
}

.nav__link:hover,
.nav__link--active {
  color: var(--text-primary);
}

.nav__link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--accent-gradient);
  transition: var(--transition-normal);
}

.nav__link:hover::after {
  width: 100%;
}

/* CTA Button */
.header__cta {
  display: none;
}

/* Mobile Menu Button */
.header__menu-btn {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 30px;
  height: 30px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: var(--z-fixed);
}

.header__menu-btn span {
  display: block;
  width: 100%;
  height: 2px;
  background: var(--text-primary);
  margin: 3px 0;
  transition: var(--transition-normal);
  transform-origin: center;
}

.menu-btn--open span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.menu-btn--open span:nth-child(2) {
  opacity: 0;
}

.menu-btn--open span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Navigation */
.header__mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--secondary-bg);
  border-bottom: 1px solid var(--border-primary);
  overflow: hidden;
}

.mobile-nav {
  padding: var(--space-6) 0;
}

.mobile-nav__list {
  list-style: none;
  margin-bottom: var(--space-6);
}

.mobile-nav__item {
  margin-bottom: var(--space-4);
}

.mobile-nav__link {
  display: block;
  color: var(--text-secondary);
  font-size: var(--text-lg);
  font-weight: 500;
  padding: var(--space-3) var(--space-4);
  transition: var(--transition-fast);
  border-left: 3px solid transparent;
}

.mobile-nav__link:hover,
.mobile-nav__link--active {
  color: var(--text-primary);
  background: var(--glass-bg);
  border-left-color: var(--accent-primary);
}

.mobile-nav__cta {
  padding: 0 var(--space-4);
}

.mobile-nav__cta .btn {
  width: 100%;
  justify-content: center;
}

/* Desktop Styles */
@media (min-width: 768px) {
  .header__nav {
    display: block;
  }
  
  .header__cta {
    display: block;
  }
  
  .header__menu-btn {
    display: none;
  }
  
  .header__mobile-menu {
    display: none;
  }
}

/* Large Desktop */
@media (min-width: 1024px) {
  .header__content {
    padding: var(--space-5) 0;
  }
  
  .nav__list {
    gap: var(--space-10);
  }
}

/* Animations */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Hover Effects */
.header__logo:hover .logo__accent::after {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
